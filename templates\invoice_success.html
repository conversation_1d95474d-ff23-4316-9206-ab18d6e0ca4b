<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="google" content="notranslate">
  <meta name="robots" content="noindex, nofollow" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Payment Confirmation</title>
  <!-- Bootstrap CSS -->
  <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.7.2/font/bootstrap-icons.min.css" rel="stylesheet">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">

      <!-- Disable Right click -->

        
    
    <!-- disable Print Screen for Windows -->

      
    
    <!-- disable print screen for mac -->

      

    <!-- disabling print screen for Linux -->

      

    <!-- disabling inspection tool -->

      
  <!-- End Google Tag Manager -->
  <style>
    
    body {
      -webkit-user-select: none; /* Disable text selection */
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      margin: 0;
        }

    body, html {
      height: 100%;
      margin: 0;
      font-family: 'Comfortaa', sans-serif !important;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      width: 100%;
      padding: 5%;
    }

    a{
      text-decoration: none !important;
    }

    #background {
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: #28a745;
      opacity: 0;
      animation: fadeInBackground 2s forwards;
    }

    @keyframes fadeInBackground {
      to {
        opacity: 1;
      }
    }

    #content {
      position: relative;
      color: white;
      font-size: 24px;
      opacity: 0;
      transition: opacity 1s;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: 20px;
    }

    .lottie-container {
      width: 200px;
      height: 200px;
    }

    .done_btn{
      padding: 8px 24px;
      border-radius: 1rem;
      font-size: 20px;
      background-color: #ffffff;
      color: #28a745;
      font-weight: 600 !important;
      border: none;
      text-decoration: none !important;
    }

    .done_btn:hover{
      color: #28a745;
    }

    @media (max-width: 768px) {
      .done_btn {
        width: 100%;
        position: fixed;
        bottom: 0;
        left: 0;
        padding: 8px 0;
        border-radius: 0;
        font-weight: 500;
        text-align: center;
      }
    }

    .invoice {
      color: white;
      background: #00000055;
      padding: 8px 16px;
      border-radius: 1rem;
      font-size: 20px;
      transition: all 0.4s;
    }

    .invoice:hover{
      color: white;
      background: #000000a5;
    }
  </style>
</head>
<body>
  

  <div id="background"></div>
  <div id="content" class="text-center">
    <audio id="myAudio">
      <source src="./success.mp3" type="audio/mpeg">
    </audio>
    <div class="lottie-container" id="lottie-animation"></div>
    <p>Invoice Created Successfully</p>
    <p>Your invoice has been created.</p>
    <p><a href="/students/invoice_student/{{ invoice.slug }}/" class="invoice">Check</a></p>
    <a href="/students/" class="done_btn">Done</a>
  </div>

  <!-- Bootstrap JS and dependencies -->
  <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
  <!-- Lottie JS -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.7.6/lottie.min.js"></script>

   
   
  <!-- Custom JavaScript -->
  <script>
    document.addEventListener("DOMContentLoaded", function() {
      setTimeout(function() {
        document.getElementById('content').style.opacity = 1;
        // Load the Lottie animation
        var animationData = {
          "nm":"Comp 1","ddd":0,"h":400,"w":400,"meta":{"g":"@lottiefiles/toolkit-js 0.33.2"},
          "layers":[{"ty":4,"nm":"tick","sr":1,"st":0,"op":91,"ip":0,"hd":false,"ddd":0,"bm":0,"hasMask":false,"ao":0,"ks":{"a":{"a":0,"k":[0,0,0],"ix":1},"s":{"a":1,"k":[{"o":{"x":0.084,"y":0.249},"i":{"x":0.35,"y":0.986},"s":[0,0,100],"t":2},{"s":[100,100,100],"t":27}],"ix":6},"sk":{"a":0,"k":0},"p":{"a":0,"k":[199.5,200.5,0],"ix":2},"r":{"a":0,"k":0,"ix":10},"sa":{"a":0,"k":0},"o":{"a":0,"k":100,"ix":11}},"ef":[],"shapes":[{"ty":"gr","bm":0,"hd":false,"mn":"ADBE Vector Group","nm":"Group 1","ix":1,"cix":2,"np":2,"it":[{"ty":"sh","bm":0,"hd":false,"mn":"ADBE Vector Shape - Group","nm":"Path 1","ix":1,"d":1,"ks":{"a":0,"k":{"c":false,"i":[[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0]],"v":[[37.193,-26.193],[-15.193,26.193],[-37.193,4.193]]},"ix":2}},{"ty":"st","bm":0,"hd":false,"mn":"ADBE Vector Graphic - Stroke","nm":"Stroke 1","lc":2,"lj":2,"ml":1,"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":12,"ix":5},"c":{"a":0,"k":[1,1,1],"ix":3}},{"ty":"tr","a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"sk":{"a":0,"k":0,"ix":4},"p":{"a":0,"k":[0,0],"ix":2},"r":{"a":0,"k":0,"ix":6},"sa":{"a":0,"k":0},"o":{"a":0,"k":100,"ix":7}}]},{"ty":"tm","bm":0,"hd":false,"mn":"ADBE Vector Filter - Trim","nm":"Trim Paths 1","ix":2,"e":{"a":0,"k":100,"ix":2},"o":{"a":0,"k":0,"ix":3},"s":{"a":1,"k":[{"o":{"x":0.084,"y":0.207},"i":{"x":0.35,"y":0.988},"s":[100],"t":12},{"s":[0],"t":37}],"ix":1},"m":1}],"ind":1},{"ty":4,"nm":"circle","sr":1,"st":0,"op":91,"ip":0,"hd":false,"ddd":0,"bm":0,"hasMask":false,"ao":0,"ks":{"a":{"a":0,"k":[0,0,0],"ix":1},"s":{"a":1,"k":[{"o":{"x":0.084,"y":0.249},"i":{"x":0.35,"y":0.986},"s":[0,0,100],"t":0},{"s":[100,100,100],"t":25}],"ix":6},"sk":{"a":0,"k":0},"p":{"a":0,"k":[199.5,200.5,0],"ix":2},"r":{"a":0,"k":0,"ix":10},"sa":{"a":0,"k":0},"o":{"a":0,"k":100,"ix":11}},"ef":[],"shapes":[{"ty":"gr","bm":0,"hd":false,"mn":"ADBE Vector Group","nm":"Group 1","ix":1,"cix":2,"np":3,"it":[{"ty":"sh","bm":0,"hd":false,"mn":"ADBE Vector Shape - Group","nm":"Path 1","ix":1,"d":1,"ks":{"a":1,"k":[{"o":{"x":0.084,"y":0.597},"i":{"x":0.35,"y":0.967},"s":[{"c":true,"i":[[0,0],[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0],[0,0]],"v":[[0.525,-0.55],[0.85,-0.45],[0.6,-0.537],[0.462,-0.563]]}],"t":5},{"s":[{"c":true,"i":[[35.84,0],[0,35.84],[-35.84,0],[0,-35.84]],"o":[[-35.84,0],[0,-35.84],[35.84,0],[0,35.84]],"v":[[0,65],[-65,0],[0,-65],[65,0]]}],"t":30}],"ix":2}},{"ty":"sh","bm":0,"hd":false,"mn":"ADBE Vector Shape - Group","nm":"Path 2","ix":2,"d":1,"ks":{"a":0,"k":{"c":true,"i":[[42.46,0],[0,-42.46],[-42.46,0],[0,42.46]],"o":[[-42.46,0],[0,42.46],[42.46,0],[0,-42.46]],"v":[[0,-77],[-77,0],[0,77],[77,0]]},"ix":2}},{"ty":"fl","bm":0,"hd":false,"mn":"ADBE Vector Graphic - Fill","nm":"Fill 1","c":{"a":0,"k":[1,1,1],"ix":4},"r":1,"o":{"a":0,"k":100,"ix":5}},{"ty":"tr","a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"sk":{"a":0,"k":0,"ix":4},"p":{"a":0,"k":[0,0],"ix":2},"r":{"a":0,"k":0,"ix":6},"sa":{"a":0,"k":0},"o":{"a":0,"k":100,"ix":7}}]}],"ind":2}],"v":"5.8.0","fr":60,"op":91,"ip":0,"assets":[]
        };

        lottie.loadAnimation({
          container: document.getElementById('lottie-animation'), // the dom element that will contain the animation
          renderer: 'svg',
          loop: true,  // Change loop to true
          autoplay: true,
          animationData: animationData
        });
      }, 2000); // Adjust timing as needed
    });
  </script>

<script>
  window.addEventListener('load', () => {
      const audio = document.getElementById('myAudio');
      audio.play().catch(error => {
          console.warn('Autoplay blocked:', error);
      });
  });
</script>
<script>
  window.addEventListener("load", function () {
      const path = window.location.pathname; // Get current page path
      let pageData = JSON.parse(localStorage.getItem("page_data")) || {}; // Get stored data or empty object
    
      // Increment count for the current path
      pageData[path] = pageData[path] ? pageData[path] + 1 : 1;
    
      // Store updated data back to localStorage
      localStorage.setItem("page_data", JSON.stringify(pageData));
    });
    
    // Function to send page view data
    function sendPageData() {
      const pageData = JSON.parse(localStorage.getItem("page_data")) || {};
    
      if (Object.keys(pageData).length > 0) {
        fetch(location.origin + "/librarian/track-page-view/", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-CSRFToken": "{{ csrf_token }}",
          },
          body: JSON.stringify(pageData),
        })
         
          .then(() => {
            localStorage.removeItem("page_data");
          })
          .catch((error) => console.error("Error sending page data:", error));
          localStorage.removeItem("page_data");
      } else {
          
        console.log("No page data to send");
      }
    }
    
    // Send data every 10 seconds
    setInterval(sendPageData, 10000);
</script>
</body>
</html>
