<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="google" content="notranslate">
    <link rel="icon" href="/static/img/librainian-logo-black-transparent.png" type="image/x-icon">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta http-equiv="content-language" content="en">
    <meta name="geo.region" content="IN">

    <title>Library Search | Find Libraries Near You | Librainian</title>
    <link rel="canonical" href="https://www.librainian.com/librarian/library-list/">

    <!-- SEO Meta Tags -->
    <meta name="robots" content="index, follow, max-image-preview:large">
    <meta name="keywords" content="library search, find libraries, study spaces, reading rooms, library facilities, library directory, library management system, libraries near me, study rooms, library search engine, Librainian">
    <meta name="description" content="Search and explore libraries registered with Librainian - The #1 Library Management System. Find study spaces, reading rooms, and library facilities near you. Get details about opening hours, contact information, and available services.">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Librainian">
    <meta property="og:title" content="Library Search | Find Libraries Near You | Librainian">
    <meta property="og:description" content="Search and explore libraries registered with Librainian. Find study spaces, reading rooms, and library facilities near you.">
    <meta property="og:url" content="https://www.librainian.com/librarian/library-list/">
    <meta property="og:image" content="https://www.librainian.com/static/img/library_search.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="en_US">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@librainian_app">
    <meta name="twitter:creator" content="@librainian_app">
    <meta name="twitter:title" content="Library Search | Find Libraries Near You | Librainian">
    <meta name="twitter:description" content="Search and explore libraries registered with Librainian. Find study spaces, reading rooms, and library facilities near you.">
    <meta name="twitter:image" content="https://www.librainian.com/static/img/library_search.jpg">

    <!-- Author and Date Info -->
    <meta itemprop="author" content="Librainian Team">
    <meta itemprop="datePublished" content="2024-01-01">
    <meta itemprop="dateModified" content="2024-07-07">

    <!-- Stylesheets -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css"

    <!-- Structured Data / JSON-LD -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": "Library Search | Find Libraries Near You | Librainian",
        "description": "Search and explore libraries registered with Librainian - The #1 Library Management System. Find study spaces, reading rooms, and library facilities near you.",
        "url": "https://www.librainian.com/librarian/library-list/",
        "publisher": {
            "@type": "Organization",
            "name": "Librainian",
            "logo": {
                "@type": "ImageObject",
                "url": "https://www.librainian.com/static/img/librainian-logo-black-transparent.png"
            }
        },
        "isPartOf": {
            "@type": "WebSite",
            "name": "Librainian",
            "url": "https://www.librainian.com/"
        },
        "breadcrumb": {
            "@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "Home",
                    "item": "https://www.librainian.com/"
                },
                {
                    "@type": "ListItem",
                    "position": 2,
                    "name": "Library Search",
                    "item": "https://www.librainian.com/librarian/library-list/"
                }
            ]
        }
    }
    </script>

    <!-- Library Directory Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ItemList",
        "itemListElement": [
            {% for l in library %}
            {
                "@type": "ListItem",
                "position": {{ forloop.counter }},
                "item": {
                    "@type": "LocalBusiness",
                    "name": "{{ l.library_name }}",
                    "description": "{{ l.description|default:'Library managed by Librainian' }}",
                    "address": {
                        "@type": "PostalAddress",
                        "addressLocality": "{{ l.librarian_address }}"
                    },
                    "telephone": "{{ l.librarian_phone_num }}",
                    "url": "https://librainian.com/librarian/library-details/{{ l.slug }}/",
                    "image": "{% if l.image %}{{ l.image.url }}{% else %}https://i.postimg.cc/wTvpt244/library-demo.jpg{% endif %}"
                }
            }{% if not forloop.last %},{% endif %}
            {% endfor %}
        ]
    }
    </script>

    <!-- Search Action Schema -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "url": "https://www.librainian.com/",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "https://www.librainian.com/librarian/library-list/?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>





    <style>
        :root {
            --primary-color: #042299;
            --secondary-color: #9bc6bf;
            --accent-color: #ff6b6b;
            --text-color: #333;
            --light-color: #f8f9fa;
            --dark-color: #212529;
            --gray-color: #6c757d;
            --success-color: #28a745;
            --border-radius: 0.5rem;
            --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            --transition: all 0.3s ease;
        }

        body {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin: 0;
            font-family: 'Poppins', sans-serif !important;
            background-color: var(--light-color);
            color: var(--text-color);
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Comfortaa', sans-serif;
            font-weight: 600;
        }

        /* Navbar Styles */
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 0.75rem 1rem;
        }

        .navbar-brand img {
            height: 40px;
        }

        .navbar .nav-link {
            font-weight: 500;
            color: var(--text-color);
            padding: 0.5rem 1rem;
            transition: var(--transition);
        }

        .navbar .nav-link:hover {
            color: var(--primary-color);
        }

        .navbar .nav-link.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, #0a4bdb 100%);
            color: white;
            padding: 4rem 0;
            position: relative;
            overflow: hidden;
            border-radius: 0 0 2rem 2rem;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('https://www.librainian.com/static/img/pattern.svg');
            background-size: cover;
            opacity: 0.1;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .hero-description {
            font-size: 1.1rem;
            opacity: 0.9;
            max-width: 700px;
            margin: 0 auto 2rem;
        }

        /* Search Box */
        .search-container {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            max-width: 700px;
            margin: 0 auto;
            transform: translateY(50%);
        }

        .search-box {
            width: 100%;
            padding: 1.25rem 4rem 1.25rem 1.5rem;
            font-size: 1.1rem;
            border: none;
            border-radius: var(--border-radius);
            outline: none;
            transition: var(--transition);
            box-shadow: var(--box-shadow);
        }

        .search-box:focus {
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.2);
        }

        .search-button {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }

        .search-button:hover {
            background-color: #031b77;
        }

        .search-icon {
            font-size: 1.25rem;
        }

        /* Filter Section */
        .filter-section {
            padding: 4rem 0 2rem;
        }

        .filter-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .filter-button {
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 2rem;
            padding: 0.5rem 1.25rem;
            font-size: 0.9rem;
            color: var(--gray-color);
            cursor: pointer;
            transition: var(--transition);
        }

        .filter-button:hover, .filter-button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .sort-dropdown {
            margin-left: auto;
        }

        /* Library Cards */
        #libraryList {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            padding: 1rem 0 3rem;
        }

        .library-item {
            display: none;
            transition: var(--transition);
        }

        .library-card {
            background-color: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
            height: 100%;
            transition: var(--transition);
            position: relative;
        }

        .library-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
        }

        .card-img-container {
            position: relative;
            height: 200px;
            overflow: hidden;
        }

        .card-img-top {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .library-card:hover .card-img-top {
            transform: scale(1.05);
        }

        .discount-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background-color: var(--accent-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-weight: 600;
            font-size: 0.9rem;
            z-index: 1;
        }

        .card-body {
            padding: 1.5rem;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--dark-color);
        }

        .card-info {
            margin-bottom: 0.75rem;
            display: flex;
            align-items: flex-start;
        }

        .card-info i {
            margin-right: 0.75rem;
            margin-top: 0.25rem;
            width: 1rem;
            text-align: center;
        }

        .card-text {
            font-size: 0.95rem;
            color: var(--gray-color);
            margin-bottom: 0.5rem;
        }

        .card-text.description {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            max-height: 4.5em;
            margin-bottom: 1.5rem;
        }

        .card-footer {
            padding: 1rem 1.5rem;
            background-color: transparent;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        .btn-view {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 2rem;
            padding: 0.6rem 1.5rem;
            font-weight: 500;
            transition: var(--transition);
            width: 100%;
        }

        .btn-view:hover {
            background-color: #031b77;
            color: white;
        }

        /* No Results Message */
        .no-results {
            text-align: center;
            padding: 3rem 0;
            color: var(--gray-color);
        }

        .no-results i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* Footer */
        .footer {
            background-color: var(--dark-color);
            color: white;
            padding: 3rem 0 1.5rem;
        }

        .footer-logo {
            height: 50px;
            margin-bottom: 1rem;
        }

        .footer-text {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            margin-bottom: 2rem;
        }

        .footer-links h5 {
            font-size: 1.1rem;
            margin-bottom: 1.25rem;
        }

        .footer-links ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-links li {
            margin-bottom: 0.75rem;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-links a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 1.5rem;
            margin-top: 2rem;
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.5);
        }

        /* Loader */
        #loader {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .spinner {
            width: 3rem;
            height: 3rem;
            border: 0.25rem solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Back to top button */
        .back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background-color: var(--primary-color);
            color: white;
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
            z-index: 99;
        }

        .back-to-top.show {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            background-color: #031b77;
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .hero-title {
                font-size: 2rem;
            }

            .search-container {
                max-width: 600px;
            }

            .search-box {
                padding: 1rem 4rem 1rem 1.25rem;
            }
        }

        @media (max-width: 768px) {
            .hero-section {
                padding: 3rem 0;
            }

            .hero-title {
                font-size: 1.75rem;
            }

            .hero-description {
                font-size: 1rem;
            }

            .search-container {
                max-width: 90%;
                transform: translateY(30%);
            }

            .search-box {
                padding: 0.9rem 3.5rem 0.9rem 1.25rem;
                font-size: 1rem;
            }

            .search-button {
                width: 2.5rem;
                height: 2.5rem;
            }

            .filter-container {
                justify-content: center;
            }

            .sort-dropdown {
                margin-left: 0;
                margin-top: 1rem;
                width: 100%;
            }

            #libraryList {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }
    </style>
</head>

<body>


    <!-- Back to top button -->
    <button class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="/static/img/librainian-logo-black-transparent.png" alt="Librainian Logo">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/librarian/library-list/">Libraries</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/blogs/p/">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/about/">About Us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/contact/">Contact</a>
                    </li>
                </ul>
                <div class="ms-lg-3 mt-3 mt-lg-0">
                    <a href="/librarian/login/" class="btn btn-outline-primary rounded-pill px-4">Login</a>
                    <a href="/librarian/signup/" class="btn btn-primary rounded-pill px-4 ms-2">Register</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content text-center">
                <h1 class="hero-title" data-aos="fade-up">Find Your Perfect Library</h1>
                <p class="hero-description" data-aos="fade-up" data-aos-delay="100">
                    Discover libraries registered with Librainian. Explore study spaces, reading rooms, and library facilities near you.
                </p>
            </div>
        </div>
        <div class="search-container" data-aos="fade-up" data-aos-delay="200">
            <input type="text" id="searchInput" class="search-box" placeholder="Search by name, location or description...">
            <button class="search-button">
                <i class="fas fa-search search-icon"></i>
            </button>
        </div>
    </section>

    <!-- Filter Section -->
    <section class="filter-section">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="filter-container">
                        <button class="filter-button active" data-filter="all">All Libraries</button>
                        <button class="filter-button" data-filter="discount">With Discount</button>
                        <div class="dropdown sort-dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                Sort By
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="sortDropdown">
                                <li><a class="dropdown-item" href="#" data-sort="name-asc">Name (A-Z)</a></li>
                                <li><a class="dropdown-item" href="#" data-sort="name-desc">Name (Z-A)</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" data-sort="discount">Highest Discount</a></li>
                            </ul>
                        </div>
                    </div>
                    <p class="text-muted small mb-4">
                        <i class="fas fa-info-circle me-1"></i> Type at least 3 characters to search. Popular libraries are shown by default.
                    </p>
                </div>
            </div>

            <!-- Library List -->
            <div id="libraryList" class="row">
                {% for l in library %}
                <div class="library-item col-12 col-md-6 col-lg-4" data-aos="fade-up" data-aos-delay="{{ forloop.counter|add:2 }}00" data-discount="{% if l.discount_available %}true{% else %}false{% endif %}" data-name="{{ l.library_name }}" data-discount-amount="{{ l.discount_amount|default:0 }}" style="display: none;">
                    <div class="library-card">
                        <div class="card-img-container">
                            <img src="{% if l.image %}{{ l.image.url }}{% else %}https://i.postimg.cc/wTvpt244/library-demo.jpg{% endif %}" class="card-img-top" alt="{{ l.library_name }}">
                            {% if l.discount_available %}
                            <div class="discount-badge">
                                <i class="fas fa-tags me-1"></i> {{ l.discount_amount }}% Off
                            </div>
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <h3 class="card-title">{{ l.library_name }}</h3>
                            <div class="card-info">
                                <i class="fas fa-location-dot text-danger"></i>
                                <div class="card-text">{{ l.librarian_address }}</div>
                            </div>
                            <div class="card-info">
                                <i class="fas fa-phone text-primary"></i>
                                <div class="card-text">{{ l.librarian_phone_num }}</div>
                            </div>
                            <div class="card-info">
                                <i class="fas fa-circle-info text-purple"></i>
                                <div class="card-text description">{{ l.description|default:"No description available." }}</div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <a href="/librarian/library-details/{{ l.slug }}/" class="btn btn-view">View Details</a>
                        </div>
                    </div>
                </div>
                {% endfor %}

                <!-- No Results Message -->
                <div class="col-12 no-results" style="display: none;">
                    <i class="fas fa-search"></i>
                    <h3>No libraries found</h3>
                    <p>Try different search terms or filters</p>
                    <button class="btn btn-outline-primary mt-3" id="resetSearch">Reset Search</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4 mb-lg-0">
                    <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" class="footer-logo">
                    <p class="footer-text">
                        Librainian is the #1 Library Management System helping libraries streamline operations, manage resources, and enhance user experience.
                    </p>
                </div>
                <div class="col-6 col-lg-2 mb-4 mb-lg-0">
                    <div class="footer-links">
                        <h5>Quick Links</h5>
                        <ul>
                            <li><a href="/">Home</a></li>
                            <li><a href="/about/">About Us</a></li>
                            <li><a href="/blogs/p/">Blog</a></li>
                            <li><a href="/contact/">Contact</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-6 col-lg-3 mb-4 mb-lg-0">
                    <div class="footer-links">
                        <h5>Services</h5>
                        <ul>
                            <li><a href="#">Library Management</a></li>
                            <li><a href="#">Student Management</a></li>
                            <li><a href="#">Resource Tracking</a></li>
                            <li><a href="#">Analytics & Reports</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="footer-links">
                        <h5>Contact Us</h5>
                        <ul>
                            <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                            <li><i class="fas fa-phone me-2"></i> +91 1234567890</li>
                            <li><i class="fas fa-map-marker-alt me-2"></i> India</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom text-center">
                <p>&copy; 2024 Librainian. All rights reserved.</p>
            </div>
        </div>
    </footer>


    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS animation library
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true
            });

            // DOM Elements
            const searchInput = document.getElementById('searchInput');
            const searchButton = document.querySelector('.search-button');
            const libraryItems = document.querySelectorAll('.library-item');
            const filterButtons = document.querySelectorAll('.filter-button');
            const sortOptions = document.querySelectorAll('.dropdown-item');
            const resetSearchButton = document.getElementById('resetSearch');
            const backToTopButton = document.getElementById('backToTop');
            const noResultsElement = document.querySelector('.no-results');

            // Current filter and sort state
            let currentFilter = 'all';
            let currentSort = 'name-asc';

            // Show loader initially
            const loader = document.getElementById('loader');
            loader.style.display = 'flex';

            // Hide loader after page loads
            window.addEventListener('load', function() {
                setTimeout(function() {
                    loader.style.display = 'none';
                }, 500);
            });

            // Show popular libraries on page load
            showPopularLibraries();

            // Focus on search input when page loads
            setTimeout(() => {
                searchInput.focus();
            }, 1000);

            // Filter libraries based on current filter and search term
            function filterLibraries() {
                const searchValue = searchInput.value.toLowerCase().replace(/\b(library|libraries|in|at|near|the)\b/g, '').trim();
                let found = false;

                libraryItems.forEach(function(item) {
                    const libraryName = item.querySelector('.card-title').textContent.toLowerCase();
                    const libraryAddress = item.querySelector('.card-info:nth-child(2)').textContent.toLowerCase();
                    const libraryDescription = item.querySelector('.description').textContent.toLowerCase();
                    const hasDiscount = item.getAttribute('data-discount') === 'true';

                    // Check if item matches search term
                    const matchesSearch = searchValue.length < 3 ||
                        libraryName.includes(searchValue) ||
                        libraryAddress.includes(searchValue) ||
                        libraryDescription.includes(searchValue);

                    // Check if item matches current filter
                    const matchesFilter = currentFilter === 'all' ||
                        (currentFilter === 'discount' && hasDiscount);

                    // Show item if it matches both search and filter
                    if (matchesSearch && matchesFilter) {
                        item.style.display = 'block';
                        found = true;
                    } else {
                        item.style.display = 'none';
                    }
                });

                // Show/hide no results message
                noResultsElement.style.display = found ? 'none' : 'block';

                // Apply current sort after filtering
                sortLibraries();
            }

            // Sort libraries based on current sort option
            function sortLibraries() {
                const libraryList = document.getElementById('libraryList');
                const items = Array.from(libraryItems);

                items.sort(function(a, b) {
                    const nameA = a.getAttribute('data-name').toLowerCase();
                    const nameB = b.getAttribute('data-name').toLowerCase();
                    const discountA = parseInt(a.getAttribute('data-discount-amount')) || 0;
                    const discountB = parseInt(b.getAttribute('data-discount-amount')) || 0;

                    switch (currentSort) {
                        case 'name-asc':
                            return nameA.localeCompare(nameB);
                        case 'name-desc':
                            return nameB.localeCompare(nameA);
                        case 'discount':
                            return discountB - discountA;
                        default:
                            return 0;
                    }
                });

                // Reorder items in the DOM
                items.forEach(function(item) {
                    libraryList.appendChild(item);
                });
            }

            // Show popular libraries (first 6)
            function showPopularLibraries() {
                let count = 0;
                libraryItems.forEach(function(item) {
                    if (count < 6) {
                        item.style.display = 'block';
                        count++;
                    } else {
                        item.style.display = 'none';
                    }
                });

                // Hide no results message
                noResultsElement.style.display = 'none';
            }

            // Reset search and filters
            function resetSearch() {
                searchInput.value = '';
                currentFilter = 'all';
                currentSort = 'name-asc';

                // Update UI to reflect reset
                filterButtons.forEach(function(button) {
                    button.classList.remove('active');
                    if (button.getAttribute('data-filter') === 'all') {
                        button.classList.add('active');
                    }
                });

                showPopularLibraries();
                searchInput.focus();
            }

            // Event Listeners

            // Search input
            searchInput.addEventListener('input', filterLibraries);

            // Search button
            searchButton.addEventListener('click', function() {
                filterLibraries();
            });

            // Filter buttons
            filterButtons.forEach(function(button) {
                button.addEventListener('click', function() {
                    // Update active state
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    // Update current filter
                    currentFilter = this.getAttribute('data-filter');

                    // Apply filter
                    filterLibraries();
                });
            });

            // Sort options
            sortOptions.forEach(function(option) {
                option.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Update current sort
                    currentSort = this.getAttribute('data-sort');

                    // Update dropdown button text
                    const sortText = this.textContent;
                    document.getElementById('sortDropdown').textContent = 'Sort: ' + sortText;

                    // Apply sort
                    sortLibraries();
                });
            });

            // Reset search button
            resetSearchButton.addEventListener('click', resetSearch);

            // Back to top button
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.add('show');
                } else {
                    backToTopButton.classList.remove('show');
                }
            });

            backToTopButton.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // Handle Enter key in search box
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    filterLibraries();
                }
            });
        });
    </script>

    <!-- Analytics Script -->
    <script>
        window.addEventListener("load", function () {
            const path = window.location.pathname; // Get current page path
            let pageData = JSON.parse(localStorage.getItem("page_data")) || {}; // Get stored data or empty object

            // Increment count for the current path
            pageData[path] = pageData[path] ? pageData[path] + 1 : 1;

            // Store updated data back to localStorage
            localStorage.setItem("page_data", JSON.stringify(pageData));
        });

        // Function to send page view data
        function sendPageData() {
            const pageData = JSON.parse(localStorage.getItem("page_data")) || {};

            if (Object.keys(pageData).length > 0) {
                fetch(location.origin + "/librarian/track-page-view/", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRFToken": "{{ csrf_token }}",
                    },
                    body: JSON.stringify(pageData),
                })
                .then(() => {
                    localStorage.removeItem("page_data");
                })
                .catch((error) => console.error("Error sending page data:", error));
                localStorage.removeItem("page_data");
            } else {
                console.log("No page data to send");
            }
        }

        // Send data every 10 seconds
        setInterval(sendPageData, 10000);
    </script>
</body>

</html>